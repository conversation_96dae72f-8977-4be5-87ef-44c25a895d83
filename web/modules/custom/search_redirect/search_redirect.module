<?php

/**
 * @file
 * Module simple pour rediriger la recherche vers /recherche
 */

use Drupal\Core\Form\FormStateInterface;

/**
 * Implements hook_form_alter().
 */
function search_redirect_form_alter(&$form, FormStateInterface $form_state, $form_id) {
  // Rediriger tous les formulaires de recherche
  if (strpos($form_id, 'search') !== FALSE) {
    $form['#action'] = '/recherche';

    if (isset($form['keys'])) {
      $form['keys']['#name'] = 'search_api_fulltext';
    }
  }
}
