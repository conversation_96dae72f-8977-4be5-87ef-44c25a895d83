<?php

/**
 * @file
 * Contains search_redirect.module.
 */

use Dr<PERSON>al\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\Core\Url;

/**
 * Implements hook_form_alter().
 */
function search_redirect_form_alter(&$form, FormStateInterface $form_state, $form_id) {
  // Debug: Log tous les form_id pour identifier le bon
  \Drupal::logger('search_redirect')->info('Form ID: @form_id', ['@form_id' => $form_id]);

  // Essayer différentes variantes du form_id
  $search_form_ids = [
    'search_block_form',
    'search-block-form',
    'search_form_block',
    'views_exposed_form'
  ];

  if (in_array($form_id, $search_form_ids) || strpos($form_id, 'search') !== FALSE) {
    // Rediriger vers la page de recherche personnalisée
    $form['#action'] = Url::fromUserInput('/recherche', [
      'language' => \Drupal::languageManager()->getCurrentLanguage()
    ])->toString();

    // Renommer l'input "keys" pour correspondre à l'exposed filter de la vue
    if (isset($form['keys'])) {
      $form['keys']['#name'] = 'search_api_fulltext';
    }

    // Log pour confirmer que la modification a été appliquée
    \Drupal::logger('search_redirect')->info('Search form redirected for form_id: @form_id', ['@form_id' => $form_id]);
  }
}

/**
 * Implements hook_form_FORM_ID_alter() for search_block_form.
 */
function search_redirect_form_search_block_form_alter(&$form, FormStateInterface $form_state, $form_id) {
  // Alternative plus spécifique pour le formulaire de recherche
  $form['#action'] = Url::fromUserInput('/recherche', [
    'language' => \Drupal::languageManager()->getCurrentLanguage()
  ])->toString();

  // Renommer l'input "keys" pour correspondre à l'exposed filter de la vue
  if (isset($form['keys'])) {
    $form['keys']['#name'] = 'search_api_fulltext';
  }

  \Drupal::logger('search_redirect')->info('Specific search block form altered');
}
