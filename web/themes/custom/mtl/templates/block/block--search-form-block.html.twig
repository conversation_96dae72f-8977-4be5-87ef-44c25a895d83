{#
/**
 * @file
 * Template pour le bloc de formulaire de recherche.
 *
 * Variables disponibles:
 * - plugin_id: L'ID du plugin du bloc.
 * - label: Le label configuré du bloc.
 * - configuration: Un tableau de la configuration du bloc.
 * - provider: Le module ou autre provider qui a fourni ce bloc.
 * - Block: L'entité bloc complète.
 * - content: Le contenu du bloc.
 * - attributes: Attributs HTML pour l'élément conteneur.
 * - title_attributes: Attributs HTML pour l'élément titre.
 * - title_prefix: Contenu supplémentaire avant le titre.
 * - title_suffix: Contenu supplémentaire après le titre.
 *
 * @see template_preprocess_block()
 */
#}
{%
  set classes = [
    'block',
    'block-' ~ configuration.provider|clean_class,
    'block-' ~ plugin_id|clean_class,
    'search-form-block',
  ]
%}
<div{{ attributes.addClass(classes) }}>
  {{ title_prefix }}
  {% if label %}
    <h2{{ title_attributes }}>{{ label }}</h2>
  {% endif %}
  {{ title_suffix }}
  {% block content %}
    <div class="search-form-wrapper">
      {{ content }}
    </div>
  {% endblock %}
</div>
