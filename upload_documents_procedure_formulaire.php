<?php

use <PERSON><PERSON><PERSON>\node\Entity\Node;
use <PERSON><PERSON>al\file\Entity\File;
use Drupal\Core\File\FileSystemInterface;

/**
 * Script d'upload uniquement des documents pour les procédures et formulaires Transport routier
 * Usage: drush php:script upload_documents_procedure_formulaire.php
 */

// Chemin vers le fichier CSV
$csv_file_path = '/var/www/html/mtl/procedure_dac.csv';

// Dossier des fichiers à uploader
$files_directory = '/var/www/html/mtl/Portail MTL - Collecte du contenu';

// Vérifier si le fichier CSV existe
if (!file_exists($csv_file_path)) {
  echo "ERREUR: Le fichier CSV n'existe pas: $csv_file_path\n";
  return;
}

// Vérifier si le dossier des fichiers existe
if (!is_dir($files_directory)) {
  echo "ERREUR: Le dossier des fichiers n'existe pas: $files_directory\n";
  return;
}

echo "=== DÉBUT DE L'UPLOAD DES DOCUMENTS PROCÉDURES/FORMULAIRES AVIATION CIVILE (DAC) ===\n";
echo "Fichier CSV: $csv_file_path\n";
echo "Dossier des fichiers: $files_directory\n\n";

// Statistiques globales
$stats = [
  'total_lines' => 0,
  'nodes_found' => 0,
  'nodes_not_found' => 0,
  'files_fr_found' => 0,
  'files_fr_not_found' => 0,
  'files_fr_uploaded' => 0,
  'files_fr_existing' => 0,
  'files_fr_errors' => 0,
  'files_ar_found' => 0,
  'files_ar_not_found' => 0,
  'files_ar_uploaded' => 0,
  'files_ar_existing' => 0,
  'files_ar_errors' => 0,
  'nodes_updated' => 0,
];

/**
 * Fonction pour trouver un fichier à partir du chemin du CSV
 */
function findFileFromCSVPath($base_directory, $csv_path) {
  if (empty($csv_path)) {
    return null;
  }
  
  echo "  → Chemin CSV: $csv_path\n";
  
  // Essayer d'abord le chemin complet tel que dans le CSV
  // Le CSV peut contenir "Portail MTL - Collecte du contenu/..." 
  $full_path = $base_directory . '/' . $csv_path;
  echo "  → Tentative chemin complet: $full_path\n";
  
  if (file_exists($full_path)) {
    echo "  ✓ Fichier trouvé par chemin complet: $full_path\n";
    return $full_path;
  }
  
  // Si le chemin complet ne marche pas, extraire juste le nom du fichier et chercher récursivement
  $filename = basename($csv_path);
  echo "  → Recherche récursive du fichier: $filename\n";
  
  $iterator = new RecursiveIteratorIterator(
    new RecursiveDirectoryIterator($base_directory, RecursiveDirectoryIterator::SKIP_DOTS),
    RecursiveIteratorIterator::LEAVES_ONLY
  );
  
  foreach ($iterator as $file) {
    if ($file->isFile() && $file->getFilename() === $filename) {
      echo "  ✓ Fichier trouvé par recherche récursive: " . $file->getPathname() . "\n";
      return $file->getPathname();
    }
  }
  
  echo "  ✗ Fichier non trouvé: $csv_path\n";
  return null;
}

/**
 * Fonction pour uploader un fichier PDF dans Drupal
 */
function uploadFileToDrupal($pdf_filename, $files_directory, $language = 'fr') {
  global $stats;
  
  if (empty($pdf_filename)) {
    return null;
  }
  
  echo "  === UPLOAD $language ===\n";
  echo "  Nom du fichier: $pdf_filename\n";
  
  // Nettoyer le chemin du fichier
  $pdf_filename = trim($pdf_filename);
  
  // Chercher le fichier à partir du chemin CSV
  $file_path = findFileFromCSVPath($files_directory, $pdf_filename);
  
  if (!$file_path || !file_exists($file_path)) {
    echo "  ✗ Fichier non accessible: $pdf_filename\n";
    $stats["files_{$language}_not_found"]++;
    return null;
  }
  
  $stats["files_{$language}_found"]++;
  
  try {
    // Vérifier si le fichier existe déjà dans Drupal par nom
    $existing_files = \Drupal::entityTypeManager()
      ->getStorage('file')
      ->loadByProperties(['filename' => basename($file_path)]);
    
    if ($existing_files) {
      $existing_file = reset($existing_files);
      echo "  ⚡ Fichier existant trouvé dans Drupal: " . $existing_file->getFilename() . " (ID: " . $existing_file->id() . ")\n";
      $stats["files_{$language}_existing"]++;
      return $existing_file;
    }
    
    // Préparer le répertoire de destination
    $destination_dir = 'public://procedure-formulaire/aviation-civile/';
    \Drupal::service('file_system')->prepareDirectory($destination_dir, FileSystemInterface::CREATE_DIRECTORY | FileSystemInterface::MODIFY_PERMISSIONS);
    
    echo "  → Lecture du fichier: $file_path\n";
    $file_data = file_get_contents($file_path);
    
    if ($file_data === FALSE) {
      echo "  ✗ Erreur lors de la lecture du fichier: $file_path\n";
      $stats["files_{$language}_errors"]++;
      return null;
    }
    
    echo "  → Taille du fichier: " . strlen($file_data) . " bytes\n";
    
    // Copier le fichier vers Drupal
    $destination = $destination_dir . basename($file_path);
    echo "  → Destination: $destination\n";
    
    // Créer l'entité fichier
    $file = \Drupal::service('file.repository')->writeData($file_data, $destination, FileSystemInterface::EXISTS_REPLACE);
    
    if ($file) {
      $file->setPermanent();
      $file->save();
      echo "  ✓ Fichier uploadé avec succès: " . $file->getFilename() . " (ID: " . $file->id() . ")\n";
      echo "  → URI: " . $file->getFileUri() . "\n";
      $stats["files_{$language}_uploaded"]++;
      return $file;
    } else {
      echo "  ✗ Erreur lors de la création du fichier dans Drupal\n";
      $stats["files_{$language}_errors"]++;
      return null;
    }
    
  } catch (Exception $e) {
    echo "  ✗ Exception lors de l'upload: " . $e->getMessage() . "\n";
    $stats["files_{$language}_errors"]++;
    return null;
  }
}

/**
 * Fonction pour vérifier si un contenu existe déjà
 */
function getExistingNode($title) {
  echo "  → Recherche du nœud procedure_formulaire (Aviation civile): $title\n";
  
  // Recherche avec tag aviation civile
  $query = \Drupal::entityQuery('node')
    ->condition('type', 'procedure_formulaire')
    ->condition('title', $title)
    ->accessCheck(FALSE);
  
  // Ajouter la condition pour le tag "aviation civile" si le champ existe
  if (\Drupal::entityTypeManager()->getStorage('node')->getEntityType()->hasKey('bundle')) {
    $query->condition('field_secteur.entity.name', 'Aviation civile');
  }
  
  $nids = $query->execute();
  
  if ($nids) {
    $node = Node::load(reset($nids));
    echo "  ✓ Nœud trouvé par titre: ID " . $node->id() . "\n";
    return $node;
  }
  
  // Recherche alternative sans le filtre secteur
  echo "  → Recherche alternative sans filtre secteur\n";
  $query = \Drupal::entityQuery('node')
    ->condition('type', 'procedure_formulaire')
    ->condition('title', $title)
    ->accessCheck(FALSE);
  
  $nids = $query->execute();
  
  if ($nids) {
    $node = Node::load(reset($nids));
    echo "  ✓ Nœud trouvé par titre (sans filtre secteur): ID " . $node->id() . "\n";
    return $node;
  }
  
  echo "  ✗ Nœud non trouvé\n";
  return null;
}

// Lire le fichier CSV complet
$csv_content = file_get_contents($csv_file_path);
if (!$csv_content) {
  echo "ERREUR: Impossible de lire le fichier CSV\n";
  return;
}

// Diviser en lignes et nettoyer les retours à la ligne dans les cellules
$lines = explode("\n", $csv_content);

// Ignorer les lignes d'en-têtes et d'exemples (lignes 1-16)
$data_lines = array_slice($lines, 16);

// Fonction pour parser une ligne CSV malformée
function parseCSVLine($line) {
  $data = str_getcsv($line, ',', '"');
  
  foreach ($data as &$cell) {
    $cell = trim(str_replace(["\n", "\r"], ' ', $cell));
  }
  
  return $data;
}

// Reconstruire les lignes brisées par les retours à la ligne
$reconstructed_lines = [];
$current_line = '';

foreach ($data_lines as $line) {
  $line = trim($line);
  
  if (empty($line)) {
    continue;
  }
  
  $current_line .= ($current_line ? ' ' : '') . $line;
  $quote_count = substr_count($current_line, '"');
  
  if ($quote_count % 2 == 0) {
    $reconstructed_lines[] = $current_line;
    $current_line = '';
  }
}

if (!empty($current_line)) {
  $reconstructed_lines[] = $current_line;
}

$line_number = 16; // Commencer après les en-têtes

// Traiter chaque ligne de données
foreach ($reconstructed_lines as $line) {
  $line_number++;
  $stats['total_lines']++;
  
  if (empty(trim($line))) {
    continue;
  }
  
  $data = parseCSVLine($line);
  
  // Vérifier que la ligne contient toutes les colonnes attendues
  if (count($data) < 8) {
    echo "LIGNE $line_number: Ignorée (colonnes incomplètes: " . count($data) . ")\n\n";
    continue;
  }
  
  // Vérifier que la ligne contient des données valides
  if (empty($data[4])) { // Titre français
    echo "LIGNE $line_number: Ignorée (titre français vide)\n\n";
    continue;
  }
  
  echo "=== LIGNE $line_number ===\n";
  
  try {
    // Extraire les données (structure différente du CSV procédures)
    $titre_fr = trim($data[4]);
    $pdf_fr = trim($data[6]);
    $pdf_ar = trim($data[7]);
    
    echo "Titre: $titre_fr\n";
    echo "PDF FR: $pdf_fr\n";
    echo "PDF AR: $pdf_ar\n";
    
    // Chercher le nœud existant
    $existing_node = getExistingNode($titre_fr);
    
    if (!$existing_node) {
      echo "✗ Nœud non trouvé - IGNORÉ\n\n";
      $stats['nodes_not_found']++;
      continue;
    }
    
    $stats['nodes_found']++;
    echo "✓ Nœud trouvé: ID " . $existing_node->id() . "\n";
    
    // Vérifier les fichiers actuels du nœud
    if ($existing_node->hasField('field_lien_telechargement')) {
      $current_files = $existing_node->get('field_lien_telechargement')->getValue();
      echo "Fichiers actuels dans le nœud: " . count($current_files) . "\n";
      
      foreach ($current_files as $file_info) {
        if (isset($file_info['target_id'])) {
          $file = File::load($file_info['target_id']);
          if ($file) {
            echo "  - " . $file->getFilename() . " (ID: " . $file->id() . ")\n";
          }
        }
      }
    }
    
    // Uploader les fichiers
    $file_fr = null;
    $file_ar = null;
    $files_to_add = [];
    
    if (!empty($pdf_fr)) {
      $file_fr = uploadFileToDrupal($pdf_fr, $files_directory, 'fr');
      if ($file_fr) {
        $files_to_add[] = [
          'target_id' => $file_fr->id(),
          'description' => 'Version française',
        ];
      }
    }
    
    if (!empty($pdf_ar)) {
      $file_ar = uploadFileToDrupal($pdf_ar, $files_directory, 'ar');
      if ($file_ar) {
        $files_to_add[] = [
          'target_id' => $file_ar->id(),
          'description' => 'Version arabe',
        ];
      }
    }
    
    // Mettre à jour le nœud avec les nouveaux fichiers
    if (!empty($files_to_add) && $existing_node->hasField('field_lien_telechargement')) {
      $current_files = $existing_node->get('field_lien_telechargement')->getValue();
      $existing_file_ids = array_column($current_files, 'target_id');
      
      $new_files = [];
      foreach ($files_to_add as $file_info) {
        if (!in_array($file_info['target_id'], $existing_file_ids)) {
          $new_files[] = $file_info;
        }
      }
      
      if (!empty($new_files)) {
        $all_files = array_merge($current_files, $new_files);
        $existing_node->set('field_lien_telechargement', $all_files);
        $existing_node->save();
        
        echo "✓ Nœud mis à jour avec " . count($new_files) . " nouveau(x) fichier(s)\n";
        echo "Total fichiers dans le nœud: " . count($all_files) . "\n";
        $stats['nodes_updated']++;
      } else {
        echo "⚡ Tous les fichiers étaient déjà présents dans le nœud\n";
      }
    } else if (empty($files_to_add)) {
      echo "✗ Aucun fichier à ajouter\n";
    }
    
  } catch (Exception $e) {
    echo "ERREUR ligne $line_number: " . $e->getMessage() . "\n";
  }
  
  echo "\n";
}

// Afficher le résumé détaillé
echo "\n";
echo "==================================================\n";
echo "            RÉSUMÉ DÉTAILLÉ FINAL\n";
echo "==================================================\n";
echo "Total lignes traitées: " . $stats['total_lines'] . "\n";
echo "\n--- NŒUDS ---\n";
echo "Nœuds trouvés: " . $stats['nodes_found'] . "\n";
echo "Nœuds non trouvés: " . $stats['nodes_not_found'] . "\n";
echo "Nœuds mis à jour: " . $stats['nodes_updated'] . "\n";
echo "\n--- FICHIERS FRANÇAIS ---\n";
echo "Fichiers FR trouvés sur disque: " . $stats['files_fr_found'] . "\n";
echo "Fichiers FR non trouvés: " . $stats['files_fr_not_found'] . "\n";
echo "Fichiers FR uploadés (nouveaux): " . $stats['files_fr_uploaded'] . "\n";
echo "Fichiers FR existants (réutilisés): " . $stats['files_fr_existing'] . "\n";
echo "Erreurs upload FR: " . $stats['files_fr_errors'] . "\n";
echo "\n--- FICHIERS ARABES ---\n";
echo "Fichiers AR trouvés sur disque: " . $stats['files_ar_found'] . "\n";
echo "Fichiers AR non trouvés: " . $stats['files_ar_not_found'] . "\n";
echo "Fichiers AR uploadés (nouveaux): " . $stats['files_ar_uploaded'] . "\n";
echo "Fichiers AR existants (réutilisés): " . $stats['files_ar_existing'] . "\n";
echo "Erreurs upload AR: " . $stats['files_ar_errors'] . "\n";
echo "\n--- TOTAUX ---\n";
echo "Total fichiers trouvés: " . ($stats['files_fr_found'] + $stats['files_ar_found']) . "\n";
echo "Total fichiers uploadés: " . ($stats['files_fr_uploaded'] + $stats['files_ar_uploaded']) . "\n";
echo "Total fichiers existants: " . ($stats['files_fr_existing'] + $stats['files_ar_existing']) . "\n";
echo "Total erreurs: " . ($stats['files_fr_errors'] + $stats['files_ar_errors']) . "\n";
echo "==================================================\n";
echo "Upload terminé!\n";